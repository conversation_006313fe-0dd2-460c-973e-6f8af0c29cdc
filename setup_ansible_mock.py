#!/usr/bin/env python3
"""
设置 Ansible Mock 模块的脚本
这个脚本会将 ansible_mock.py 复制到 Python 的 site-packages 目录中，
使其可以作为 ansible 模块被导入
"""

import os
import sys
import shutil
import site

def setup_ansible_mock():
    """设置 ansible mock 模块"""
    
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    mock_file = os.path.join(current_dir, 'ansible_mock.py')
    
    if not os.path.exists(mock_file):
        print(f"错误: 找不到 {mock_file}")
        return False
    
    # 获取 site-packages 目录
    site_packages = site.getsitepackages()
    if not site_packages:
        print("错误: 无法找到 site-packages 目录")
        return False
    
    # 使用第一个 site-packages 目录
    target_dir = site_packages[0]
    target_file = os.path.join(target_dir, 'ansible.py')
    
    try:
        # 复制文件
        shutil.copy2(mock_file, target_file)
        print(f"成功: ansible mock 模块已安装到 {target_file}")
        
        # 创建 __init__.py 文件（如果需要的话）
        init_file = os.path.join(target_dir, 'ansible', '__init__.py')
        ansible_dir = os.path.join(target_dir, 'ansible')

        if not os.path.exists(ansible_dir):
            os.makedirs(ansible_dir)
            with open(init_file, 'w') as f:
                f.write('# Ansible Mock Package\nimport sys\nimport os\nsys.path.insert(0, os.path.dirname(__file__))\nfrom ansible import *\n')
            print(f"成功: 创建了 ansible 包目录 {ansible_dir}")
        
        return True
        
    except Exception as e:
        print(f"错误: 安装 ansible mock 模块时出错: {e}")
        return False

def test_import():
    """测试是否可以成功导入 ansible"""
    try:
        import ansible
        print(f"成功: 可以导入 ansible 模块，版本: {getattr(ansible, '__version__', 'unknown')}")
        return True
    except ImportError as e:
        print(f"错误: 无法导入 ansible 模块: {e}")
        return False

if __name__ == "__main__":
    print("开始设置 Ansible Mock 模块...")
    
    if setup_ansible_mock():
        print("\n测试导入...")
        if test_import():
            print("\n✅ Ansible Mock 模块设置成功!")
            print("现在您的项目应该可以导入 ansible 模块了（虽然功能是模拟的）")
        else:
            print("\n❌ 设置完成但导入测试失败")
    else:
        print("\n❌ Ansible Mock 模块设置失败")

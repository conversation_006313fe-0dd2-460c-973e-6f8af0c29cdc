"""
Ansible Mock Module
这是一个简单的 ansible 模拟模块，用于解决 Windows 环境下 ansible 安装问题
当项目代码尝试导入 ansible 时，可以使用这个模拟模块避免 ImportError
"""

class AnsibleMock:
    """Ansible 模拟类"""
    
    def __init__(self):
        self.version = "2.9.13-mock"
    
    def run_playbook(self, *args, **kwargs):
        """模拟运行 playbook"""
        print("Warning: Using Ansible Mock - playbook execution is disabled")
        return {"status": "mock", "message": "Ansible functionality is mocked"}
    
    def run_command(self, *args, **kwargs):
        """模拟运行命令"""
        print("Warning: Using Ansible Mock - command execution is disabled")
        return {"status": "mock", "message": "Ansible functionality is mocked"}

# 创建全局实例
ansible = AnsibleMock()

# 模拟常用的 ansible 模块和函数
def run_playbook(*args, **kwargs):
    return ansible.run_playbook(*args, **kwargs)

def run_command(*args, **kwargs):
    return ansible.run_command(*args, **kwargs)

# 模拟 ansible 的常用属性
__version__ = "2.9.13-mock"
VERSION = "2.9.13-mock"

# 如果需要，可以添加更多的模拟功能
class PlaybookExecutor:
    def __init__(self, *args, **kwargs):
        print("Warning: Using Ansible Mock - PlaybookExecutor is mocked")
    
    def run(self):
        return {"status": "mock", "message": "Playbook execution is mocked"}

class Runner:
    def __init__(self, *args, **kwargs):
        print("Warning: Using Ansible Mock - Runner is mocked")
    
    def run(self):
        return {"status": "mock", "message": "Command execution is mocked"}

# 导出常用的类和函数
__all__ = ['ansible', 'run_playbook', 'run_command', 'PlaybookExecutor', 'Runner', '__version__', 'VERSION']

# DevOps Platform Requirements - Windows Compatible Version
# 已测试并成功安装的依赖包

# Core Django Framework
Django==3.2
djangorestframework==3.11.1
django-cors-headers==3.5.0
django-redis==4.12.1
djangorestframework-simplejwt==4.6.0
django-celery-beat==2.0.0
django-timezone-field==4.2.3
django-filter==2.3.0
django-q==1.3.9
django-picklefield==3.1

# API Documentation
drf-yasg==1.20.0
drf-nested-routers==0.93.4
coreapi==2.3.3
coreschema==0.0.4

# Database
PyMySQL==1.0.2  # 替代 mysqlclient，纯Python实现
redis==3.5.3

# LDAP (替代 python-ldap)
ldap3==2.9.1  # 纯Python LDAP客户端，替代 python-ldap

# Task Queue
celery==4.4.7

# WebSocket Support
channels==3.0.4
channels-redis==3.2.0
daphne==3.0.2

# HTTP & Requests
requests==2.31.0
requests-oauthlib==1.3.0
requests-toolbelt==1.0.0

# Image Processing
Pillow==10.1.0

# Data Processing
numpy==1.26.1
pandas==2.1.1

# Excel Processing
openpyxl==3.1.2

# Search Engine
elasticsearch==7.15.1
elasticsearch-dsl==7.4.0

# Container Orchestration
kubernetes==17.17.0

# SSH & Remote Access
paramiko==2.7.2

# Version Control & CI/CD Integration
python-gitlab==3.15.0
python-jenkins==1.7.0
jira==3.4.1

# Process Management
supervisor==4.2.5
psutil==5.8.0

# Authentication & Security
bcrypt==4.3.0
cryptography==3.4.8
PyJWT==2.5.0
pycryptodome==3.9.8

# Utilities
arrow==1.3.0
blessed==1.21.0
croniter==1.3.8
python-dateutil==2.8.2
python-crontab==3.3.0
pytz==2020.1
six==1.15.0
packaging==25.0
inflection==0.5.1
itypes==1.2.0
uritemplate==3.0.1
tqdm==4.65.0
xmltodict==0.12.0

# Web Framework Extensions
gunicorn==20.1.0  # 如果需要的话

# Ansible (Mock Implementation)
# 由于Windows长路径限制，使用本地mock实现
# 文件: ansible.py (ansible_mock.py的副本)

# 注意：以下包由于Windows编译问题被跳过或替代：
# - ansible==2.9.13  # 替代为：本地ansible.py模拟模块
# - mysqlclient==2.0.1  # 替代为：PyMySQL==1.0.2
# - python-ldap==3.3.1  # 替代为：ldap3==2.9.1

# 如果需要ansible功能，建议：
# 1. 启用Windows长路径支持
# 2. 安装Microsoft C++ Build Tools
# 3. 然后安装：ansible==2.9.13

# 如果需要原生MySQL客户端，建议：
# 1. 安装Microsoft C++ Build Tools
# 2. 然后安装：mysqlclient==2.0.1

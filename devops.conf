server {
    listen 80;
    server_name localhost;
    access_log off;
    error_page 404 /404.html;
    location = /404.html {
        root /etc/nginx;
    }
    error_page 500 502 503 504 /500.html;
    location = /500.html {
        root /etc/nginx;
    }
    underscores_in_headers on;
    client_max_body_size   2048m;

    location / {
        root /app/devops-backend/dist;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }
    location /ws/ {
        proxy_pass http://localhost:8081;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
    }
    location ~ ^/(admin|api) {
        proxy_pass http://localhost:8080;
        proxy_connect_timeout    1200s;
        proxy_read_timeout       1200s;
        proxy_send_timeout       1200s;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}


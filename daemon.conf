[unix_http_server]
file=/tmp/supervisor.sock                       ; path to your socket file

[supervisord]
logfile=/var/log/supervisord.log    		; supervisord log file
logfile_maxbytes=50MB                           ; maximum size of logfile before rotation
logfile_backups=10                              ; number of backed up logfiles
loglevel=error                                  ; info, debug, warn, trace
pidfile=/var/run/supervisord.pid                ; pidfile location
nodaemon=true                                   ; run supervisord as a daemon
minfds=1024                                     ; number of startup file descriptors
minprocs=200                                    ; number of process descriptors
user=root                                       ; default user
childlogdir=/var/log/               		; where child log files will live

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock           ; use a unix:// URL  for a unix socket

[program:nginx]
command=nginx -g 'daemon off;'
autostart=true
autorestart=true
redirect_stderr=true
priority=1
[program:backend]
command=gunicorn devops_backend.wsgi -b 0.0.0.0:8080 -t 60 --thread 20
directory=/app/devops-backend
autostart=true
autorestart=true
startsecs=3
priority=1
stopasgroup=true
killasgroup=true
stderr_logfile=/dev/sederr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
[program:websocket]
command=daphne devops_backend.asgi:application -b 0.0.0.0 -p 8081
directory=/app/devops-backend
autostart=true
autorestart=true
startsecs=3
priority=1
stopasgroup=true
killasgroup=true
stderr_logfile=/dev/sederr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
[program:qtask]
command=python manage.py qcluster
directory=/app/devops-backend
autostart=true
autorestart=true
startsecs=3
priority=1
stopasgroup=true
killasgroup=true
stderr_logfile=/dev/sederr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
[program:celery]
command=./celery.sh
directory=/app/devops-backend
autostart=true
autorestart=true
startsecs=3
priority=1
stopasgroup=true
killasgroup=true
stderr_logfile=/dev/sederr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0


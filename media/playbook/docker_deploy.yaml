---
- hosts: "{{ target_hosts }}"  #"{{ hosts }}"
  gather_facts: no
  vars:
    ansible_python_interpreter: /usr/bin/python3
    # 静态文件源路径
    src_path: "/data/nfs/web/{{ src_env }}/{{ app_name }}/{{ src_tag }}"
    # 生产仓库
    reg_path: "/data/nfs/web/{{ dest_env }}/{{ app_name }}"

  tasks:
    - name: Python3 Dependency
      pip:
        name: docker
    # 平台直接访问目标机器
    - name: Docker Login
      docker_login:
        registry_url: "{{ harbor_url }}"
        username: "{{ harbor_user }}"
        password: "{{ harbor_passwd }}"
        reauthorize: yes
    - name: Pull Image
      docker_image:
        name: "{{ image }}"
        source: pull

    - name: Update Container
      docker_container:
        name: "{{ app_name }}"
        image: "{{ image }}"
        command:
          - "{{ service_command }}"
        state: started
        recreate: true
        env:
          appiddd: "backend"
          appnamee: "migrate"

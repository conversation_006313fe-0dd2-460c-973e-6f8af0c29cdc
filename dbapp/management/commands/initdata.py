#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
<AUTHOR> <PERSON>
@Contact : <EMAIL>
@Time : 2021/2/4 下午10:09
@FileName: initdata.py
@Blog ：https://imaojia.com
"""

from __future__ import unicode_literals
import time

from django.core.management.base import BaseCommand, CommandError
from django.db.models import Q

from dbapp.model.model_ucenter import Menu, Organization, Permission, Role, SystemConfig, DataDict

from config import COMPANY


class Command(BaseCommand):
    help = '初始化DevOps运维平台数据.'
    model_menu = Menu
    model_perm = Permission
    model_datadict = DataDict

    def add_arguments(self, parser):
        parser.add_argument('--type', type=str, choices=['all', 'system', 'rbac', 'datadict', 'workflow'],
                            help='system: 系统基本设置, rbac: 初始化rbac数据, datadict: 初始化数据字典, workflow: 初始化工单模板, all: 初始化所有数据')

    def insert_data(self, model, title, data):
        for i in data:
            m = model()
            if i:
                self.stdout.write(f"Create [rbac] {title}: {i['name']}")
                for k, v in i.items():
                    if k == 'parent':
                        v = model.objects.get(name=v)
                    setattr(m, k, v)
                try:
                    m.save()
                    self.stdout.write(self.style.SUCCESS(
                        f"Create [rbac] {title}: {i['name']} done."))
                except BaseException as e:
                    self.stdout.write(self.style.ERROR(
                        f"Create [rbac] {title}: {i['name']} failed, reason: {e}"))

    def insert(self, model, title, data, field='name'):
        for i in data:
            if i:
                self.stdout.write(
                    f"Create [{model._meta.object_name}] {title}: {i[field]}")
                try:
                    parent = i.pop('parent', None)
                    try:
                        parent_obj = model.objects.get(**{field: parent})
                    except:
                        parent_obj = None
                    model.objects.create(parent=parent_obj, **i)
                    self.stdout.write(
                        self.style.SUCCESS(f"Create [{model._meta.object_name}] {title}: {i[field]} done."))
                except BaseException as e:
                    self.stdout.write(
                        self.style.ERROR(f"Create [{model._meta.object_name}] {title}: {i[field]} failed, reason: {e}"))
                    if e.args[0] == 1062:
                        # 已存在记录更新
                        self.stdout.write(
                            f"Update [{model._meta.object_name}] {title}: {i[field]}")
                        model.objects.filter(**{field: i[field]}).update(**i)
                    if e.args[0] == 1052:
                        # 重试
                        self.stdout.write(
                            self.style.ERROR(f"Retry create [{model._meta.object_name}] {title}: {i[field]}."))
                        time.sleep(1)
                        model.objects.create(parent=parent_obj, **i)

    def init_system(self):
        self.stdout.write('Start init system config')
        try:
            SystemConfig.objects.create(name='platform', type='platform',
                                        config={"url": "http://localhost/", "access": 120, "refresh": 3000,
                                                "whitelist": [{"url": "/api/user/login/"}, {"url": "/api/user/logout/"},
                                                              {"url": "/api/user/profile/info/"},
                                                              {"url": "/api/user/profile/menus/"}],
                                                "password": "", "port": 25})
            self.stdout.write(self.style.SUCCESS(
                'System base config init success.'))
        except BaseException as e:
            self.stdout.write(self.style.ERROR(
                f"System base config init failed, reason: {e}"))
        try:
            Organization.objects.create(
                name=COMPANY, dept_id='0', type='company')
            self.stdout.write(self.style.SUCCESS('Company setup success.'))
        except BaseException as e:
            self.stdout.write(self.style.ERROR(
                f"Company setup  failed, reason: {e}"))

    def init_rbac(self):
        self.stdout.write('Start init RBAC data')
        from rest_framework.schemas.openapi import SchemaGenerator
        generator = SchemaGenerator(title='DevOps API')
        perms = []
        try:
            generator.get_schema()
        except BaseException as e:
            self.stdout.write(str(e))
        finally:
            data = generator.endpoints
            parent = None
            for ep in data:
                try:
                    parent = ep[2].cls.perms_map[1]['*'][1]
                except BaseException as e:
                    parent = None
                try:
                    for p in ep[2].cls.perms_map:
                        for k, v in p.items():
                            temp = {}
                            if type(v) is tuple:
                                temp = {'name': v[1].upper(), 'method': v[0]}
                            if k != '*' and parent:
                                temp['parent'] = parent
                            perms.append(temp)
                    for p in ep[2].initkwargs.get('perms_map', []):
                        for k, v in p.items():
                            temp = {}
                            if type(v) is tuple:
                                temp = {'name': v[1].upper(), 'method': v[0]}
                            if k != '*' and parent:
                                temp['parent'] = parent
                            perms.append(temp)
                except BaseException as e:
                    pass
            perms = [dict(t) for t in set([tuple(d.items()) for d in perms])]
            perms = sorted(perms, key=lambda x: 1 if 'parent' in x else 0)
        Permission.objects.update_or_create(name='管理员', method='admin')
        self.insert(self.model_perm, 'Permission', perms)
        # # 插入菜单
        menus = [{'name': 'Asset', 'path': '/asset', 'redirect': '/asset/app/project', 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 10, 'component': 'Layout', 'title': 'CMDB管理', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None}, {'name': 'application', 'path': 'app', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 10, 'component': 'assets/index', 'title': '应用管理', 'icon': 'application', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Asset'}, {'name': 'applicationlist', 'path': 'list', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 10, 'component': 'assets/platform/app', 'title': '应用列表', 'icon': 'applicationList', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'application'}, {'name': 'Environment', 'path': 'environment', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 10, 'component': 'assets/platform/environment', 'title': '应用环境', 'icon': 'environment', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'application'}, {'name': 'language', 'path': 'language', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 20, 'component': 'assets/languageBuild/languageBuild', 'title': '开发语言', 'icon': 'language', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'application'}, {'name': 'assets', 'path': 'asset', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 10, 'component': 'assets/asset/index', 'title': '资产管理', 'icon': 'assets', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Asset'}, {'name': 'db', 'path': 'db', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 1, 'component': 'assets/asset/db/list', 'title': '数据库实例', 'icon': 'form', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'assets'}, {'name': 'IDC', 'path': 'idc', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 10, 'component': 'assets/itasset/IDC', 'title': 'IDC管理', 'icon': 'IDC', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'assets'}, {'name': 'Project', 'path': 'project', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 10, 'component': 'assets/projectManagement/index', 'title': '项目管理', 'icon': 'projectManage', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Asset'}, {'name': 'area', 'path': 'area', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 10, 'component': 'assets/projectManagement/area', 'title': '产品列表', 'icon': 'area', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Project'}, {'name': 'ProjectList', 'path': 'list', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 10, 'component': 'assets/projectManagement/project', 'title': '项目列表', 'icon': 'project', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Project'}, {'name': 'region', 'path': 'region', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 10, 'component': 'assets/projectManagement/region', 'title': '区域列表', 'icon': 'region', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Project'}, {'name': 'configcenter', 'path': 'configcenter', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 20, 'component': 'assets/configCenter/index', 'title': '配置中心', 'icon': 'el-icon-s-grid', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Asset'}, {'name': 'cmdbdashboard', 'path': 'dashboard', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 1000, 'component': 'assets/index', 'title': '资产概览', 'icon': 'list', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Asset'}, {'name': 'Deploy', 'path': '/deploy', 'redirect': '/deploy/cd', 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 20, 'component': 'Layout', 'title': '构建发布', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None}, {'name': 'cicd', 'path': 'cicd', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 5, 'component': 'deploy/appCicd', 'title': '持续构建部署', 'icon': 'deploy', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Deploy'}, {'name': 'admindeploy', 'path': 'admin/cd', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 30, 'component': 'deploy/continuedDeployForAdmin', 'title': '持续部署-管理员', 'icon': 'component', 'affix': False, 'single': False, 'activeMenu': '/deploy/admin/cd', 'parent': 'Deploy'}, {'name': 'admindeployenv', 'path': 'admin/cdd', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 999, 'component': 'deploy/deployEnvForAdmin', 'title': '持续部署-管理员', 'icon': 'deploy', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Deploy'}, {'name': 'deploydashboard', 'path': 'dashboard', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 999, 'component': 'deploy/overview/index', 'title': 'CICD报表', 'icon': 'list', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Deploy'}, {'name': 'jardependency', 'path': 'jar/dependency', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'deploy/jarBuild/index', 'title': 'JAR依赖包', 'icon': 'tree-table', 'affix': False, 'single': False, 'activeMenu': '', 'parent': 'Deploy'}, {'name': 'jarbuild', 'path': 'build', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'deploy/jarBuild/jarDependencyBuild', 'title': '依赖包构建', 'icon': 'tree', 'affix': False, 'single': False, 'activeMenu': '', 'parent': 'jardependency'}, {'name': 'jarproject', 'path': 'project', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'deploy/jarBuild/jarDependency', 'title': '依赖包工程', 'icon': 'list', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'jardependency'}, {'name': 'Kubernetes', 'path': '/kubernetes', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 40, 'component': 'Layout', 'title': '容器管理', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None}, {'name': 'k8slist', 'path': 'cluster', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 10, 'component': 'assets/k8s/k8sList', 'title': '集群管理', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Kubernetes'}, {'name': 'k8s', 'path': 'cluster/:clusterId/detail', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 20, 'component': 'assets/k8s/k8s', 'title': '集群详情', 'icon': 'list', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Kubernetes'}, {'name': 'kubernetesnamespace', 'path': 'cluster/:clusterId/namespace', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 20, 'component': 'assets/k8s/namespace', 'title': '命名空间', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Kubernetes'}, {'name': 'kubernetesworkload', 'path': 'cluster/:clusterId/workload', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 30, 'component': 'assets/k8s/workload', 'title': '工作负载', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Kubernetes'}, {'name': 'kubernetesservice', 'path': 'cluster/:clusterId/service', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 40, 'component': 'assets/k8s/service', 'title': '服务访问', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Kubernetes'}, {'name': 'kubernetespod', 'path': 'cluster/:clusterId/pod', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 50, 'component': 'assets/k8s/pod', 'title': '容器组', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Kubernetes'}, {'name': 'clusteroperatepanel', 'path': 'cluster/:clusterId/panel', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 60, 'component': 'assets/k8s/operatePanel/index', 'title': '操作面板', 'icon': 'el-icon-s-help', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'Kubernetes'}, {'name': 'workflow', 'path': '/workflow', 'redirect': '/workflow/workbench', 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 50, 'component': 'Layout', 'title': '工单管理', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None}, {'name': 'workflowworkbench', 'path': 'workbench', 'redirect': '/workflow/workbench/request', 'is_frame': False, 'hidden': False, 'spread': True, 'sort': 100, 'component': 'workflow/workbench', 'title': '工作台', 'icon': 'el-icon-s-claim', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflow'}, {'name': 'workflowrequest', 'path': 'request', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 1, 'component': 'workflow/my-request/requestIndex', 'title': '发起工单', 'icon': 'skill', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflowworkbench'}, {'name': 'WorkflowMyUpcoming', 'path': 'my-upcoming', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 5, 'component': 'workflow/my-upcoming/index', 'title': '我的待办', 'icon': 'el-icon-alarm-clock', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflowworkbench'}, {'name': 'workflowmyrequest', 'path': 'my-request', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 30, 'component': 'workflow/my-request/index', 'title': '我的申请', 'icon': 'people', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflowworkbench'}, {'name': 'WorkflowMyRelated', 'path': 'my-related', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 40, 'component': 'workflow/my-related/index', 'title': '我的关联', 'icon': 'el-icon-star-off', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflowworkbench'}, {'name': 'workflowdraftbox', 'path': 'draft-box', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 41, 'component': 'workflow/draft-box/index', 'title': '我的草稿', 'icon': 'el-icon-s-release', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflowworkbench'}, {'name': 'workflowall', 'path': 'all', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 50, 'component': 'workflow/all/index', 'title': '所有工单', 'icon': 'el-icon-wallet', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflowworkbench'}, {'name': 'workflowdetail', 'path': ':wid/detail', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 999, 'component': 'workflow/all/detail', 'title': '工单详情', 'icon': '', 'affix': False, 'single': False, 'activeMenu': '/workflow/workbench/all', 'parent': 'workflowworkbench'}, {'name': 'workflowhandle', 'path': ':wid/handle', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 999, 'component': 'workflow/my-upcoming/process', 'title': '工单处理', 'icon': '', 'affix': False, 'single': False, 'activeMenu': '/workflow/workbench/my-upcoming', 'parent': 'workflowworkbench'}, {'name': 'publishorder', 'path': 'release', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 200, 'component': 'deploy/release/list', 'title': '发版工单', 'icon': 'chart', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflow'}, {'name': 'sqlworkflow', 'path': 'sql', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 300, 'component': 'sqlworkflow/list', 'title': 'SQL工单', 'icon': 'documentation', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflow'}, {'name': 'mergerequest', 'path': 'mergerequest', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 600, 'component': 'deploy/mergerequest', 'title': '合并请求管理', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflow'}, {'name': 'WorkflowCategory', 'path': 'category', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'workflow/category/index', 'title': '工单分类', 'icon': 'el-icon-s-grid', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflow'}, {'name': 'workflowtemplate', 'path': 'template', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'workflow/template/index', 'title': '工单模板', 'icon': 'excel', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflow'}, {'name': 'continuedeployorder', 'path': 'release/:id/detail', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   'sort': 1000, 'component': 'deploy/release/releaseDetails', 'title': '版本发布', 'icon': 'nested', 'affix': False, 'single': False, 'activeMenu': '/workflow/release', 'parent': 'workflow'}, {'name': 'sqldetail', 'path': 'sql/:id/detail', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 1000, 'component': 'sqlworkflow/detail', 'title': 'SQL工单详情', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': '/workflow/sql', 'parent': 'workflow'}, {'name': 'workflowfault', 'path': 'fault', 'redirect': '/workflow/fault/faultlist', 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 1005, 'component': 'workflow/fault/index', 'title': '事件工单', 'icon': 'fault', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflow'}, {'name': 'Fault', 'path': 'faultlist', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'workflow/fault/fault', 'title': '工单列表', 'icon': 'fault-list', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflowfault'}, {'name': 'FaultDetail', 'path': ':id/detail', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 999, 'component': 'workflow/fault/module/fault-workflow-detail', 'title': '工单详情', 'icon': 'el-icon-s-grid', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflowfault'}, {'name': 'FaultOver', 'path': 'faultover', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'workflow/fault-echart', 'title': '报障统计', 'icon': 'fault-echart', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflowfault'}, {'name': 'MaintainEquipment', 'path': 'maintainequipment', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'workflow/fault/maintain-equipment', 'title': '维修列表', 'icon': 'fault-maintain', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'workflowfault'}, {'name': 'loganalysis', 'path': '/loganalysis', 'redirect': '/loganalysis/discover', 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'Layout', 'title': '日志分析', 'icon': 'excel', 'affix': False, 'single': False, 'activeMenu': None}, {'name': 'discover', 'path': 'discover', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 10, 'component': 'monitor/logManage/discover', 'title': '日志查询', 'icon': 'chart', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'loganalysis'}, {'name': 'eslogdownload', 'path': 'downloads', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 20, 'component': 'monitor/logManage/download', 'title': '日志下载', 'icon': 'link', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'loganalysis'}, {'name': 'elasticmanage', 'path': 'elasticsearch', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'monitor/logManage/elastic/index', 'title': 'ElasticSearch实例', 'icon': 'documentation', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'loganalysis'}, {'name': 'monitor', 'path': '/monitor', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 999, 'component': 'Layout', 'title': '监控中心', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None}, {'name': 'prometheus', 'path': 'prometheus', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 1, 'component': 'monitor/prometheus/index', 'title': 'Prometheus', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'monitor'}, {'name': 'prometheus_instance', 'path': 'instance', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'monitor/prometheus/instance/index', 'title': '实例管理', 'icon': '', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'prometheus'}, {'name': 'prometheus_metric', 'path': 'metric', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'monitor/prometheus/metric/index', 'title': '监控指标', 'icon': '', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'prometheus'}, {'name': 'prometheus_rules', 'path': 'rules', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'monitor/prometheus/rules-template/index', 'title': '告警规则模板', 'icon': '', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'prometheus'}, {'name': 'alertcenter', 'path': 'alert-center', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 5, 'component': 'monitor/alert-center/index', 'title': '告警中心', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'monitor'}, {'name': 'alert_info', 'path': 'alert-info', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 1, 'component': 'monitor/alert-center/alert-info/index', 'title': '告警信息', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'alertcenter'}, {'name': 'alert_group', 'path': 'alert-group', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 20, 'component': 'monitor/alert-center/alert-group/index', 'title': '告警组', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'alertcenter'}, {'name': 'alert_robot', 'path': 'alert-robot', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 30, 'component': 'monitor/alert-center/alert-robot/index', 'title': '通知群机器人', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'alertcenter'}, {'name': 'alert_log', 'path': 'alert-log', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 40, 'component': 'monitor/alert-center/log/index', 'title': '告警详细日志', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'alertcenter'}, {'name': 'alert_log_silences', 'path': 'silences', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 50, 'component': 'monitor/alert-center/silences/index', 'title': '告警沉默', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'alertcenter'}, {'name': 'alertmanager', 'path': 'alertmanager', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 10, 'component': 'monitor/alert-manager/index', 'title': 'AlertManager', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'monitor'}, {'name': 'monitor_dashboard', 'path': 'dashboard', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 10, 'component': 'monitor/dashboard/index', 'title': '监控面板', 'icon': 'list', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'monitor'}, {'name': 'monitor_settings', 'path': 'settings', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 30, 'component': 'monitor/settings/index', 'title': '监控设置', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'monitor'}, {'name': 'monitor_duty', 'path': 'duty', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 40, 'component': 'monitor/duty/index', 'title': '值班表配置', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'monitor'}, {'name': 'consul', 'path': 'consul', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 999, 'component': 'monitor/consul/index.vue', 'title': 'Consul', 'icon': 'list', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'monitor'}, {'name': 'exporter', 'path': 'exporter', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'monitor/consul/exporter-manager.vue', 'title': 'Expo', 'icon': 'list', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'consul'}, {'name': 'monitor_charts', 'path': 'charts', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 999, 'component': 'monitor/charts/index', 'title': '监控图表', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'monitor'}, {'name': 'monitor_host', 'path': 'host', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'monitor/charts/host/index', 'title': '主机监控', 'icon': '', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'monitor_charts'}, {'name': 'monitor_k8s', 'path': 'kubernetes', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'monitor/charts/k8s/index', 'title': '容器监控', 'icon': 'list', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'monitor_charts'}, {'name': 'telephone', 'path': 'telephone', 'redirect': None, 'is_frame': False, 'hidden': True, 'spread': False, 'sort': 999, 'component': 'monitor/telephone_alarm/index.vue', 'title': '告警电话', 'icon': 'list', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'monitor'}, {'name': 'interface-document', 'path': 'interface-document', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'monitor/telephone_alarm/module/InterfaceDocumentDrawer.vue', 'title': '接口文档', 'icon': 'list', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'telephone'}, {'name': 'sign-log', 'path': 'sign-log', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'monitor/telephone_alarm/module/key-manage.vue', 'title': '密钥管理', 'icon': 'list', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'telephone'}, {'name': 'telephone-logs', 'path': 'telephone-logs', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'monitor/telephone_alarm/module/telephone-logs.vue', 'title': '告警记录', 'icon': 'list', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'telephone'}, {'name': 'sqlworkbench', 'path': '/sql', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'Layout', 'title': 'SQL工作台', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None}, {'name': 'sqlquery', 'path': 'query', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'sqlworkflow/query', 'title': 'SQL查询', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'sqlworkbench'}, {'name': 'sqladvisor', 'path': 'advisor', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 1000, 'component': 'sqlworkflow/advisor/advisor', 'title': 'SQL优化', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'sqlworkbench'}, {'name': 'System', 'path': '/system', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'Layout', 'title': '系统管理', 'icon': 'table', 'affix': False, 'single': False, 'activeMenu': None}, {'name': 'Menu', 'path': 'menu', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 10, 'component': 'system/menu/list', 'title': '菜单管理', 'icon': 'list', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'System'}, {'name': 'Role', 'path': 'role', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 20, 'component': 'system/role/list', 'title': '角色管理', 'icon': 'theme', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'System'}, {'name': 'User', 'path': 'user', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 30, 'component': 'system/user/list', 'title': '用户管理', 'icon': 'user', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'System'}, {'name': 'Organization', 'path': 'organization', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 40, 'component': 'system/organization/list', 'title': '组织架构', 'icon': 'peoples', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'System'}, {'name': 'DictData', 'path': 'dictionary', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 50, 'component': 'system/dictionary/list', 'title': '数据字典管理', 'icon': 'zip', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'System'}, {'name': 'AuditLog', 'path': 'audit', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 666, 'component': 'system/audit/list', 'title': '日志审计', 'icon': 'chart', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'System'}, {'name': 'SystemConfig', 'path': 'config', 'redirect': None, 'is_frame': False, 'hidden': False, 'spread': False, 'sort': 999, 'component': 'system/config/list', 'title': '系统设置', 'icon': 'setting', 'affix': False, 'single': False, 'activeMenu': None, 'parent': 'System'}]
        self.insert(self.model_menu, 'Menu', menus)
        # 插入默认角色
        try:
            Role.objects.create(name='默认角色', desc='用户默认角色')
        except BaseException as e:
            pass
        role = Role.objects.get(name='默认角色')
        try:
            role_perms = Permission.objects.filter(
                Q(method__contains='userinfo_') | Q(method='data_list'))
            role.permissions.add(*[i.id for i in role_perms])
        except BaseException as e:
            pass

    def init_datadict(self):
        self.stdout.write('Start init Data Dictionary')
        # 插入数据字典
        data = [
            {'key': 'CATEGORY', 'value': '应用类别', 'extra': '{}', 'desc': ''},
            {'key': 'category.server', 'value': '后端',
                'extra': '{"key":"Jenkinsfile"}', 'desc': '', 'parent': 'CATEGORY'},
            {'key': 'category.front', 'value': '前端',
                'extra': '{"key":"JenkinsfileWeb"}', 'desc': '', 'parent': 'CATEGORY'},
            {'key': 'POSITION', 'value': '职位', 'extra': '{}', 'desc': ''},
            {'key': 'position.op', 'value': '运维',
                'extra': '{\n    "name": "运维人员"\n}', 'desc': '', 'parent': 'POSITION'},
            {'key': 'position.test', 'value': '测试',
                'extra': '{\n    "name": "测试人员"\n}', 'desc': '', 'parent': 'POSITION'},
            {'key': 'position.dev', 'value': '开发',
                'extra': '{\n    "name": "开发人员"\n}', 'desc': '', 'parent': 'POSITION'},
            {'key': 'position.product', 'value': '产品',
                'extra': '{\n    "name": "产品人员"\n}', 'desc': '', 'parent': 'POSITION'},
            {'key': 'BRAND', 'value': '品牌', 'extra': '{}', 'desc': ''},
            {'key': 'brand.dell', 'value': 'DELL',
                'extra': '{}', 'desc': '', 'parent': 'BRAND'},
            {'key': 'brand.hp', 'value': 'HP', 'extra': '{}',
                'desc': '', 'parent': 'BRAND'},
            {'key': 'LOCATION', 'value': '地标', 'extra': '{}', 'desc': ''},
            {'key': 'location.shenzhen', 'value': '深圳',
                'extra': '{}', 'desc': '', 'parent': 'LOCATION'},
            {'key': 'location.shanghai', 'value': '上海',
                'extra': '{}', 'desc': '', 'parent': 'LOCATION'},
            {'key': 'IDC_SUPPLIER', 'value': 'IDC服务商', 'extra': '{}', 'desc': ''},
            {'key': 'idc_supplier.aliyun', 'value': '阿里云',
                'extra': '{\n    "type": 0,\n    "method": "import_aliyun"\n}', 'desc': '', 'parent': 'IDC_SUPPLIER'},
            {'key': 'idc_supplier.hwcloud', 'value': '华为云',
                'extra': '{\n    "type": 1,\n    "method": "import_hwcloud"\n}', 'desc': '', 'parent': 'IDC_SUPPLIER'},
            {'key': 'idc_supplier.aws', 'value': 'AWS',
                'extra': '{}', 'desc': '', 'parent': 'IDC_SUPPLIER'},
            {'key': 'idc_supplier.tencent', 'value': '腾讯云',
                'extra': '{}', 'desc': '', 'parent': 'IDC_SUPPLIER'},
            {'key': 'PUBLISH_TIME_DIFF', 'value': '发版时间差', 'extra': '{\n "hours": 24 \n}',
                'desc': '定义当前时间与期望发版时间差范围.\n超出该时间差范围则不允许发版'},
            {'key': 'DEPLOY_CHECK', 'value': '部署结果检测策略', 'extra': '{\n    "count": 40,\n    "interval": 5\n}',
                'desc': '循环检测部署结果\ncount: 检测次数\ninterval: 检测间隔'},
            {'key': 'REMOTE_HOSTS', 'value': '*************',
                'extra': '{}', 'desc': '静态文件中转机器'},
            {'key': 'HEALTH_CHECK', 'value': '健康检测', 'extra': '{}', 'desc': ''},
            {'key': 'health_check.TCP', 'value': 'TCP', 'extra': '{\n    "readinessProbe": {\n        "tcpSocket": {"port": 8080}, \n        "initialDelaySeconds": 10, \n        "timeoutSeconds": 6, \n        "periodSeconds": 3\n    }, \n    "livenessProbe": {\n        "tcpSocket": {"port": 8080}, \n        "initialDelaySeconds": 1800, \n        "timeoutSeconds": 20, \n        "periodSeconds": 10\n    }\n}', 'desc': '', 'parent': 'HEALTH_CHECK'},
            {'key': 'health_check.HTTP', 'value': 'HTTP', 'extra': '{\n    "readinessProbe": {\n        "httpGet": {"path": "/actuator/health", "port": 8070}, \n        "initialDelaySeconds": 10, \n        "timeoutSeconds": 6, \n        "periodSeconds": 3\n    }, \n    "livenessProbe": {\n        "httpGet": {"path": "/actuator/health", "port": 8070}, \n        "initialDelaySeconds": 1800, \n        "timeoutSeconds": 20, \n        "periodSeconds": 10\n    }\n}', 'desc': '', 'parent': 'HEALTH_CHECK'},
            {'key': 'health_check.COMMAND', 'value': 'COMMAND', 'extra': '{\n    "readinessProbe":{\n        "exec": {"command":"echo HelloWorld"},\n        "initialDelaySeconds": 10,\n        "periodSeconds": 5,\n        "timeoutSeconds": 10\n        \n    },\n    "livenessProbe":{\n        "exec":{"command":""},\n        "initialDelaySeconds":15,\n        "periodSeconds":15,\n        "timeoutSeconds":10\n    }\n}', 'desc': '', 'parent': 'HEALTH_CHECK'},
            {'key': 'ORDER_NOTIFY_DELAY', 'value': 'ORDER_NOTIFY_DELAY',
                'extra': '{\n    "delay": 60\n}', 'desc': '工单通知延迟'},
            {'key': 'REGION', 'value': '区域', 'extra': '{}', 'desc': ''},
            {'key': 'region.eg', 'value': '埃及', 'extra': '{}',
                'desc': '', 'parent': 'REGION'},
            {'key': 'region.cn', 'value': '中国', 'extra': '{}',
                'desc': '', 'parent': 'REGION'},
            {'key': 'region.id', 'value': '印尼', 'extra': '{}',
                'desc': '', 'parent': 'REGION'},
            {'key': 'region.mx', 'value': '墨西哥',
                'extra': '{}', 'desc': '', 'parent': 'REGION'},
            {'key': 'region.br', 'value': '巴西', 'extra': '{}',
                'desc': '', 'parent': 'REGION'},
            {'key': 'region.sa', 'value': '阿联酋',
                'extra': '{}', 'desc': '', 'parent': 'REGION'},
            {'key': 'region.sg', 'value': '新加坡',
                'extra': '{}', 'desc': '', 'parent': 'REGION'},
            {'key': 'APM', 'value': 'APM探针', 'extra': '{}', 'desc': ''},
            {'key': 'apm.bonree', 'value': 'bonree', 'extra': '{\n    "image": "basic/bonree-agent",\n    "tag": "v1",\n    "command": "mkdir -p /bonree-agent && cp -r /opt/bonree-agent/* /bonree-agent && chown -R 1000 /bonree-agent"\n}', 'desc': '', 'parent': 'APM'},
            {'key': 'apm.skywalking', 'value': 'skywalking',
                'extra': '{\n    "image": "basic/skywalking-agent",\n    "tag": "v4",\n    "command": "mkdir -p /skywalking/agent && cp -a /opt/skywalking/agent/* /skywalking/agent"\n}', 'desc': '', 'parent': 'APM'},
            {'key': 'RESOURCES', 'value': '资源规格', 'extra': '{}', 'desc': ''},
            {'key': 'resources.s', 'value': 'S', 'extra': '{\n    "limits": {\n        "cpu": "1000m",\n        "memory": "1Gi"\n    },\n    "requests": {\n        "cpu": "100m",\n        "memory": "800Mi"\n    }\n}', 'desc': '', 'parent': 'RESOURCES'},
            {'key': 'resources.m', 'value': 'M', 'extra': '{\n    "limits": {\n        "cpu": "2000m",\n        "memory": "2Gi"\n    },\n    "requests": {\n        "cpu": "100m",\n        "memory": "1.5Gi"\n    }\n}', 'desc': '', 'parent': 'RESOURCES'},
            {'key': 'resources.l', 'value': 'L', 'extra': '{\n    "limits": {\n        "cpu": "2000m",\n        "memory": "4Gi"\n    },\n    "requests": {\n        "cpu": "200m",\n        "memory": "2Gi"\n    }\n}', 'desc': '', 'parent': 'RESOURCES'},
            {'key': 'resources.xs', 'value': 'xs', 'extra': '{\n    "limits": {\n        "cpu": "500m",\n        "memory": "0.5Gi"\n    },\n    "requests": {\n        "cpu": "100m",\n        "memory": "0.5Gi"\n    }\n}', 'desc': '', 'parent': 'RESOURCES'},
            {'key': 'resources.xl', 'value': 'XL', 'extra': '{\n    "limits": {\n        "cpu": "4000m",\n        "memory": "4Gi"\n    },\n    "requests": {\n        "cpu": "100m",\n        "memory": "2Gi"\n    }\n}', 'desc': '', 'parent': 'RESOURCES'},
            {'key': 'CONFIG', 'value': '应用CICD关联配置', 'extra': '{}', 'desc': ''},
            {'key': 'config.jms', 'value': 'JMS系统',
                'extra': '{\n"dev": {\n   "git_branch": "dev",\n   "category": \n     {\n       "front": ["1.1.1.1"],\n       "server": ["k8s"]\n     }\n  }\n}', 'desc': ''},
            {'key': 'WORKFLOW_CONFIG', 'value': '工单系统配置', 'extra': '{}', 'desc': ''},
            {'key': 'workflow.config.upload.path', 'value': '/data/nfs/ydevops',
                'extra': '{}', 'desc': '工单系统文件上传路径', 'parent': 'WORKFLOW_CONFIG'},
            {'key': 'filter', 'value': '条件过滤', 'extra': '{\n    "*": {\n        "term": "等于",\n        "exclude": "不等于",\n        "contains": "包含"\n    },\n    "3": {\n        "term": "等于",\n        "gt": "大于",\n        "gte": "大于等于",\n        "lt": "小于",\n        "lte": "小于等于",\n        "range": "范围"\n    },\n    "4": {\n        "term": "等于",\n        "gt": "大于",\n        "gte": "大于等于",\n        "lt": "小于",\n        "lte": "小于等于",\n        "range": "范围"\n    },\n    "6": {\n        "gt": "大于",\n        "gte": "大于等于",\n        "lt": "小于",\n        "lte": "小于等于",\n        "range": "范围"\n    },\n    "7": {\n        "gt": "大于",\n        "gte": "大于等于",\n        "lt": "小于",\n        "lte": "小于等于",\n        "range": "范围"\n    },\n    "rel": {\n        "regexp": "包含",\n        "exclude": "不包含"\n    }\n}', 'desc': 'CMDB资产过滤条件'},
            {'key': 'CMDB', 'value': 'CMDB资产配置', 'extra': '{}', 'desc': ''},
            {'key': 'cmdb.import_limit', 'value': '5', 'extra': '{}',
                'desc': 'CMDB资产上报级别划分: 大于当前值归类到批量上报', 'parent': 'CMDB'},
            {'key': 'cmdb.batch_limit', 'value': '2', 'extra': '{}',
                'desc': 'CMDB资产每批处理限制', 'parent': 'CMDB'},
            {'key': 'cmdb.cmdb_table_node', 'value': '资产菜单节点配置',
                'extra': '{\n    "cloud_": "云资产",\n    "net_": "网络设备",\n    "middleware_": "中间件",\n    "storage_": "存储",\n    "idc_": "IT资产"\n}', 'desc': 'CMDB资产表菜单上一级菜单名称', 'parent': 'CMDB'},
            {'key': 'cmdb.cmdb_component', 'value': 'assets/itasset/resource',
                'extra': '{}', 'desc': '资产表菜单前端组件', 'parent': 'CMDB'},
            {'key': 'cmdb.table_icon', 'value': '资产表图标', 'extra': '{\n    "cloud_region": "asset_map",\n    "cloud_zone": "Cloud-Server",\n    "cloud_mysql": "polardb",\n    "middleware_redis": "redis",\n    "middleware_elasticsearch": "elasticsearch",\n    "middleware_rocketmq": "rocketmq",\n    "cloud_loadbalance": "loadbalance",\n    "storage_oss": "oss",\n    "storage_nas": "nas",\n    "cloud_cdn": "CDN",\n    "cloud_waf": "waf",\n    "cloud_kubernetes": "k8s",\n    "cloud_instance": "ECS",\n    "cloud_domain": "domain"\n}', 'desc': '动态资产表图标', 'parent': 'CMDB'},
            {'key': 'cmdb.field_icon', 'value': '字段状态图标', 'extra': '{\n    "server_status": {"power_on": "on", "power_off": "off"},\n    "rack_status": {"online": "on", "offline": "off"},\n    "status": {"Running": "on", "Starting": "load", "Stopping": "load", "Stopped": "off", "Pending": "load"}\n}', 'desc': '资产字段显示图标', 'parent': 'CMDB'},
            {'key': 'cmdb.search_index', 'value': '全局搜索索引',
                'extra': '{\n    "index": ["idc_*", "cloud_*"],\n    "key": ["vm_ip", "cloud_privateip", "ips", "ip"]\n}', 'desc': "index: 搜索多个索引用','分隔；排除索引在名称前加'-'；\nkey: 支持全局搜索的字段名", 'parent': 'CMDB'},
            {'key': 'cmdb.table', 'value': '资产表',
                'extra': '[\n    {"name": "serverasset", "alias": "云主机", "icon": "Cloud-Server"},\n    {"name": "dbasset", "alias": "云数据库", "icon": "form"},\n    {"name": "loadbalancer", "alias": "负载均衡"},\n    {"name": "redis", "alias": "Redis实例"},\n    {"name": "rocketmq", "alias": "消息队列"}\n]', 'desc': '', 'parent': 'CMDB'},
            {'key': 'DASHBOARD', 'value': '报表配置', 'extra': '{}', 'desc': ''},
            {'key': 'dashboard.cmdb', 'value': 'CMDB概览',
                'extra': '[\n {"key": "区域", "value": "cmdb.product", "type": "rds"},\n {"key": "项目", "value": "cmdb.project", "type": "rds"},\n {"key": "应用", "value": "cmdb.microapp", "type": "rds"},\n {"key": "应用模块", "value": "cmdb.appinfo", "type": "rds"}\n]', 'desc': '', 'parent': 'DASHBOARD'},
            {'key': 'dashboard.limit', 'value': '12',
                'extra': '{}', 'desc': '', 'parent': 'DASHBOARD'},
            {'key': 'TIMERANGE', 'value': '时间选择', 'extra': '[\n        [\n          { "index": "5-minutes", "item": "最近5分钟" },\n          { "index": "today", "item": "今天" }\n        ],\n        [\n          { "index": "15-minutes", "item": "最近15分钟" },\n          { "index": "yesterday", "item": "昨天" }\n        ],\n        [\n          { "index": "30-minutes", "item": "最近30分钟" },\n          { "index": "2-days", "item": "最近两天" }\n        ],\n        [\n          { "index": "1-hours", "item": "最近1小时" },\n          { "index": "7-days", "item": "最近7天" }\n        ],\n        [\n          { "index": "week", "item": "本周" },\n          { "index": "lastweek", "item": "上周" }\n        ],\n        [\n          { "index": "3-hours", "item": "最近3小时" },\n          { "index": "1-months", "item": "最近1个月" }\n        ],\n        [\n          { "index": "6-hours", "item": "最近6小时" },\n          { "index": "3-months", "item": "最近3个月" }\n        ],\n        [\n          { "index": "12-hours", "item": "最近12小时" },\n          { "index": "6-months", "item": "最近6个月" }\n        ],\n        [\n          { "index": "24-hours", "item": "最近24小时" },\n          { "index": "1-years", "item": "最近1年" }\n        ]\n      ]', 'desc': ''},
            {'key': 'timerange.cmdb', 'value': 'CMDB概览时间选择', 'extra': '[\n    { "index": "1-hours", "item": "最近1小时" },\n    { "index": "week", "item": "本周" },\n    { "index": "24-hours", "item": "最近24小时" },\n    { "index": "lastweek", "item": "上周" },\n    { "index": "3-hours", "item": "最近3小时" },\n    { "index": "1-months", "item": "最近1个月" } ,\n    { "index": "6-hours", "item": "最近6小时" },\n    { "index": "3-months", "item": "最近3个月" },\n    { "index": "12-hours", "item": "最近12小时" },\n    { "index": "6-months", "item": "最近6个月" },\n    { "index": "7-days", "item": "最近7天" },\n    { "index": "1-years", "item": "最近1年" }\n]', 'desc': '', 'parent': 'TIMERANGE'},
            {'key': 'signDocument', 'value': '签名机制',
                'extra': "### 签名机制  \n#### 构造规范化请求字符串  \nURL编码的编码规则如下：  \n\n1. 1 对于字符 A-Z、a-z、0-9以及字符“-”、“_”、“.”、“~”不编码  \n2. 2 对于其他字符编码成“%XY”（大写）的格式，其中XY是字符对应ASCII码的16进制表示。比如英文的双引号（”）对应的编码就是%22\n3. 3 对于扩展的UTF-8字符，编码成“%XY%ZA…”的格式\n4. 4 英文空格（ ）被编码为加号（+）  \n\n#### 构造签名字符串\n\n##### 请求方法字符串\n  将HTTP请求方法（GET、PUT、POST等）转化为大写\n  \n##### 接口相对路径字符串\n  获取请求路径中的相对路径部分，并对相对路径进行URL编码；上报接口相对路径/api/asset/upload/import/, URL编码后为 \n  ```%2Fapi%2Fasset%2Fupload%2Fimport%2F```\n  \n##### 请求参数字符串\n  * 用&符号分隔请求中的参数键值对\n  * 对所有参数键值对按ASCII码升序顺序进行排序\n  * 对排序后的各参数键值对以等号（=）分开参数名和参数值，对参数名和参数值分别使用UTF-8字符集进行URL编码（注意没有值的参数使用空字符串，编码规则同上URL编码的编码规则）， 用=连接参数名与参数值，参数名在前，形成新的参数键值对\n  * 用&连接各参数键值对\n  \n##### 请求头字符串\n  X-Api-SignHeaders中请求头名称列表为计算签名所用到的请求头, 当前值固定为 'X-Api-Timestamp,X-Api-Nonce'\n  \n  * 用逗号(,)分隔X-Api-SignHeaders中的请求头名称\n  * 将参与签名的请求头名称删除前导空格和后随空格\n  * 对请求头名称以ASCII码顺序进行排序\n  * 获取请求头对应的值，并将值中连续空格转化为单个空格\n  * 将请求头名称转换为小写，请求头名称放在前面，用冒号:与请求头对应的值连接，形成请求头键值串\n  * 用换行符\\\\n连接各请求头键值串（注意最终形成的字符串以换行符\\\\n结尾）\n  \n##### 拼接签名原文\n  签名原文由上面4个字符串构成，拼接的顺序为请求方式字符串、接口相对路径字符串、请求参数字符串和请求头字符串\n  各个字符串之间用换行符\\\\n连接。 签名原文字符串的拼接规则为：\n  > 请求方式字符串 + \\\\n + 接口相对路径字符串 + \\\\n + 请求参数字符串 + \\\\n + 请求头字符串\n  \n##### 生成签名串\n  首先使用HMAC-SHA1算法对上一步中获得的签名原文进行加密签名以16进制格式输出，然后将生成的签名串使用Base64编码，即可获得最终的签名串\n  请求参数X-Api-Signature的值为生成的签名串", 'desc': ''},
            {'key': 'COL_WIDTH', 'value': '表格列宽',
                'extra': '[\n    {\n        "label":"超短","value": 80\n    },\n    {\n       "label": "默认宽度","value": 180\n    },\n    {\n        "label":"中","value": 180\n    },\n    {\n       "label": "宽","value": 240\n    },\n    {\n        "label":"超宽","value": 320\n    },\n    {\n       "label": "自定义","value": 100\n    }\n]', 'desc': '表格列宽, 默认宽度为180'},
            {'key': 'SYSTEM', 'value': '系统使用配置', 'extra': '{}', 'desc': ''},
            {'key': 'system.fields', 'value': '系统占用字段',
                'extra': '["region"]', 'desc': '系统占用字段', 'parent': 'SYSTEM'},
            {'key': 'ROLLBACK_TYPE', 'value': '发布应用的回退类型',
                'extra': '{ "0": "线上BUG", "1": "数据库脚本未执行", "2": "错误应用版本", "3": "关联发版未执行 ", "999": "其他" }', 'desc': ''},
            {'key': 'ROBOTS', 'value': '机器人类型',
                'extra': '[\n  {"key": "feishu", "value": "飞书", "func": "FeishuNotify"},\n  {"key": "dingtalk", "value": "钉钉", "func": "DingtalkNotify"},\n  {"key": "wework", "value": "企业微信", "func": "WeWorkRobot"}\n]', 'desc': ''},
            {'key': 'ACCESSORIES', 'value': '硬件名称', 'extra': '{}', 'desc': '硬件名称'},
            {'key': 'accessories.disk', 'value': '硬盘',
                'extra': '{}', 'desc': '', 'parent': 'ACCESSORIES'},
            {'key': 'accessories.memory_card', 'value': '内存卡',
                'extra': '{}', 'desc': '', 'parent': 'ACCESSORIES'},
            {'key': 'accessories.cpu', 'value': 'cpu',
                'extra': '{}', 'desc': '', 'parent': 'ACCESSORIES'},
            {'key': 'accessories.network_card', 'value': '网卡',
                'extra': '{}', 'desc': '', 'parent': 'ACCESSORIES'},
            {'key': 'VENDOR', 'value': '厂商', 'extra': '{}', 'desc': '厂商'},
            {'key': 'vendor.dell', 'value': 'dell',
                'extra': '{}', 'desc': '', 'parent': 'VENDOR'},
            {'key': 'vendor.H3C', 'value': 'H3C',
                'extra': '{}', 'desc': '', 'parent': 'VENDOR'},
            {'key': 'DEPLOY_PATH', 'value': '部署路径', 'extra': '{}',
                'desc': '前端/Jar包部署路径\n默认读取default路径；\n可定制指定产品部署路径: {"devops": "/data/temp"}'},
            {'key': 'deploy_path.web', 'value': '前端部署路径',
                'extra': '{\n    "default": "/data/web"\n}', 'desc': '上级部署路径', 'parent': 'DEPLOY_PATH'},
            {'key': 'deploy_path.jar', 'value': 'Jar包部署路径',
                'extra': '{\n    "default": "/data/jar",\n    "devops": "/data/devops"\n}', 'desc': '上级部署路径', 'parent': 'DEPLOY_PATH'},
            {'key': 'MONITOR', 'value': '监控配置', 'extra': '{}', 'desc': ''},
            {'key': 'monitor.non_k8s_filter', 'value': 'Prometheus标签选择',
                'extra': '[\n    { "name": "env", "value": "环境" }, \n    { "name": "product", "value": "产品" }, \n    { "name": "project", "value": "项目" }\n]', 'desc': '非k8s部署的prometheus标签选择', 'parent': 'MONITOR'},
            {'key': 'monitor.k8s_filter', 'value': 'Prometheus标签选择',
                'extra': '[\n    { "name": "namespace", "value": "命名空间" }, \n    { "name": "container", "value": "应用" }\n]', 'desc': 'k8s部署的Prometheus标签选择', 'parent': 'MONITOR'},
            {'key': 'monitor.metric_operate', 'value': '指标运算',
                'extra': '[\n    {"name": "汇总", "value": "sum{}", "desc": "sum()"},\n    {"name": "平均值", "value": "avg{}", "desc": "avg()"},\n    {"name": "百分比", "value": "avg(rate{})", "desc": "avg(rate())"}\n]', 'desc': '', 'parent': 'MONITOR'},
            {'key': 'monitor.metric_operate.汇总', 'value': 'sum{}', 'extra': '{}',
                'desc': 'sum()', 'parent': 'monitor.metric_operate'},
            {'key': 'monitor.metric_operate.平均值', 'value': 'avg{}',
                'extra': '{}', 'desc': 'avg()', 'parent': 'monitor.metric_operate'},
            {'key': 'monitor.metric_operate.百分比',
                'value': 'avg(rate{})', 'extra': '{}', 'desc': 'avg(rate())', 'parent': 'monitor.metric_operate'},
            {'key': 'monitor.graph_type', 'value': '图表类型',
                'extra': '[{"label": "折线图", "value": "line"}, {"label": "柱状图", "value": "bar"}, {"label": "饼图", "value": "pie"}, {"label": "表格", "value": "table"}]', 'desc': '{"label": "单数值柱状图", "value": "bar-single"}', 'parent': 'MONITOR'},
            {'key': 'monitor.alert.aliyun.product', 'value': '阿里云监控产品代号映射', 'extra': '{"acs_adb": "分析型数据库MySQL版3.0", "acs_ads": "分析型数据库", "acs_airec": "智能推荐", "acs_apigateway_dashboard": "API网关", "acs_cdn": "CDN", "acs_cen": "云企业网", "acs_containerservice_dashboard": "容器服务Swarm版", "": "云解析PrivateZone", "acs_dcdn": "全站加速", "acs_ddos": "DDoS高防IP", "acs_drds": "分布式关系型数据库服务 DRDS", "acs_directmail": "邮件推送", "acs_ecs_dashboard": "云服务器ECS", "acs_ehpc_dashboard": "弹性高性能计算E-HPC", "acs_vpc_eip": "弹性公网IP", "acs_emr": "E-MapReduce", "acs_ens": "边缘节点服务ENS", "acs_ess_dashboard": "弹性伸缩", "acs_elasticsearch": "Elasticsearch", "acs_fc": "函数计算", "acs_fnf": "函数工作流", "acs_gdb": "图数据库", "acs_hybriddb": "分析型数据库 PostgreSQL版", "acs_global_acceleration": "全球加速", "acs_hbaseue": "HBase增强版", "acs_hbase": "Hbase", "acs_ipv6trans": "IPv6转换服务", "acs_bandwidth_package": "共享带宽", "acs_iot": "物联网平台", "acs_kvstore": "云数据库Redis版", "acs_kafka": "消息队列 Kafka", "acs_kubernetes": "容器服务Kubernetes版", "acs_mns_new": "消息服务 MNS", "acs_mps": "媒体处理", "acs_mq": "消息队列 MQ", "acs_maxcompute_prepay": "MaxCompute", "acs_memcache": "云数据库Memcache版", "acs_mongodb": "云数据库MongoDB版", "acs_nat_gateway": "NAT网关", "acs_newbgpddos": "新BGP高防IP", "acs_ocs": "云数据库Memcache（旧版）", "acs_oss_dashboard": "对象存储 OSS", "acs_openad": "营销引擎", "acs_opensearch": "开放搜索", "acs_pcdn": "PCDN", "acs_rds_dashboard": "云数据库RDS版", "acs_rds_sar": "云数据库专属主机组", "acs_streamcompute": "流计算", "acs_rocketmq": "消息队列 RocketMQ", "acs_smartag": "智能接入网关", "acs_scdn": "安全加速", "acs_slb_dashboard": "负载均衡", "acs_sls_dashboard": "日志服务", "acs_smc": "服务器迁移中心", "acs_hitsdb": "时序时空数据库", "acs_uis": "极致互联网服务", "acs_vod": "视频点播", "acs_express_connect": "高速通道", "acs_vpn": "VPN 网关", "acs_vs": "视频监控", "acs_videolive": "视频直播", "waf": "Web应用防火墙", "acs_hdfs": "文件存储HDFS", "acs_amqp": "消息队列 AMQP", "acs_baas": "区块链服务", "acs_batchcomputenew": "批量计算", "acs_bds": "HBase数据同步服务", "acs_cds": "云数据库Cassandra版", "acs_cfw": "云防火墙", "acs_clickhouse": "云数据库Clickhouse", "acs_ddh": "专有宿主机", "acs_ddosdip": "DDoS高防（国际）", "acs_hbaseserverless": "HBase Serverless版", "acs_hbr": "混合云备份服务", "acs_hcs_sgw": "云存储网关", "acs_hdr": "混合云容灾", "acs_hologres": "交互式分析", "acs_ipv6_bandwidth": "IPv6网关", "acs_k8s": "k8s", "acs_learn": "机器学习", "acs_nas": "文件存储NAS", "acs_oos": "运维编排", "acs_openAPI": "阿里云 OpenAPI", "acs_petadata": "HybridDB for MySQL", "acs_polardb": "云数据库POLARDB", "acs_privatelink": "私网连接", "acs_ros": "资源编排", "acs_tag": "标签"}', 'desc': '', 'parent': 'MONITOR'},
            {'key': 'monitor.job_config', 'value': 'JobName配置',
                'extra': '{\n    "configmap": {"name": "prometheus-config", "key": "prometheus.yml"},\n    "secret": {"name": "additional-config", "key": "prometheus-additional.yaml"}\n}', 'desc': '', 'parent': 'MONITOR'},
            {'key': 'monitor.global_config', 'value': '全局配置',
                'extra': '{\n    "configmap": {"name": "prometheus-config", "key": "prometheus.yml"},\n    "secret": {"name": "prometheus-k8s", "key": "prometheus.yaml.gz"}\n}', 'desc': '', 'parent': 'MONITOR'},
            {'key': 'TEMPLATE', 'value': '应用模板', 'extra': '{}', 'desc': ''},
            {'key': 'template.app', 'value': '后端应用模板',
                'extra': '{\n    "type": 0,\n    "template": {\n            "strategy": {\n                "replicas": 1,\n                "revisionHistoryLimit": 1,\n                "minReadySeconds": 3,\n                "maxSurge": "100%",\n                "maxUnavailable": "50%"\n            },\n            "resources": {\n                "limits": {\n                    "cpu": "2000m",\n                    "memory": "1.5Gi"\n                },\n                "requests": {\n                    "cpu": "300m",\n                    "memory": "1Gi"\n                }\n            },\n            "env": [\n                {\n                    "name": "JAVA_MEM_OPTS",\n                    "value": "-Xms512m -Xmx512m -Xmn196m -Xss512k -XX:MetaspaceSize=256m -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=8000 "\n                },\n                {\n                    "name": "APP_START_OPS",\n                    "value": "--server.port=8080 --management.server.port=8070  --apollo.meta=http://configserver:8080"\n                }\n            ]\n    }\n}', 'desc': '', 'parent': 'TEMPLATE'},
            {'key': 'YAML', 'value': 'K8s模板', 'extra': '{}', 'desc': ''},
            {'key': 'yaml.app', 'value': 'K8s后端模板',
                'extra': 'apiVersion: extensions/v1beta1\nkind: Deployment\nmetadata:\n  name:  MOUDLE\n  namespace:  NAMESPACE\nspec:\n  replicas: 1\n  revisionHistoryLimit: 1\n  minReadySeconds: 3\n  strategy:\n    rollingUpdate:\n      maxSurge: 100%\n      maxUnavailable: 50%\n  template:\n    metadata:\n      annotations:\n        prometheus.io/port: "8070"\n        prometheus.io/scrape: "true"\n        prometheus.io/path: "/actuator/prometheus"\n      labels:\n        app:  MOUDLE\n    spec:\n      containers:\n      - name:  MOUDLE\n        readinessProbe:\n          httpGet:\n            path: /actuator/health\n            port: 8070\n          initialDelaySeconds: 10\n          timeoutSeconds: 5\n          periodSeconds: 3\n        livenessProbe:\n          httpGet:\n            path: /actuator/health\n            port: 8070\n          initialDelaySeconds: 300\n          timeoutSeconds: 10\n          periodSeconds: 3\n        image: xxx-jms-IMAGE\n        securityContext:\n          runAsUser: 1000\n          allowPrivilegeEscalation: false\n#        command:\n#          - sleep\n#          - "36000"\n        lifecycle:\n          preStop:\n            exec:\n              command: ["/bin/sh","-c","echo ok"]\n        resources:\n          limits:\n            cpu: 2000m\n            memory: 1.5Gi\n          requests:\n            cpu: 300m\n            memory: 1Gi', 'desc': '', 'parent': 'YAML'},
            {'key': 'yaml.svc', 'value': 'SVC配置', 'extra': '{\n    "prod": {\n            "apiVersion": "v1",\n            "kind": "Service",\n            "metadata": {\n                "name": "appname",\n                "namespace": "env-product",\n                "labels": {\n                    "app": "appname"\n                }\n            },\n            "spec": {\n                "ports": [{\n                    "port": 8080,\n                    "targetPort": 8080,\n                    "protocol": "TCP",\n                    "name": "http"\n                }],\n                "selector": {\n                    "app": "appname"\n                }\n            }\n        },\n        "dev": {\n            "apiVersion": "v1",\n            "kind": "Service",\n            "metadata": {\n                "name": "appname",\n                "namespace": "env-product",\n                "labels": {\n                    "app": "appname"\n                }\n            },\n            "spec": {\n                "ports": [{\n                    "port": 8080,\n                    "targetPort": 8080,\n                    "protocol": "TCP",\n                    "name": "http"\n                }],\n                "selector": {\n                    "app": "appname"\n                }\n            }\n        },\n        "test": {\n            "apiVersion": "v1",\n            "kind": "Service",\n            "metadata": {\n                "name": "appname",\n                "namespace": "env-product",\n                "labels": {\n                    "app": "appname"\n                }\n            },\n            "spec": {\n                "ports": [{\n                    "port": 8080,\n                    "targetPort": 8080,\n                    "protocol": "TCP",\n                    "name": "http"\n                }],\n                "selector": {\n                    "app": "appname"\n                }\n            }\n        },\n        "uat": {\n            "apiVersion": "v1",\n            "kind": "Service",\n            "metadata": {\n                "name": "appname",\n                "namespace": "env-product",\n                "labels": {\n                    "app": "appname"\n                }\n            },\n            "spec": {\n                "ports": [{\n                    "port": 8080,\n                    "targetPort": 8080,\n                    "protocol": "TCP",\n                    "name": "http"\n                }],\n                "selector": {\n                    "app": "appname"\n                }\n            }\n        },\n        "pre": {\n            "apiVersion": "v1",\n            "kind": "Service",\n            "metadata": {\n                "name": "appname",\n                "namespace": "env-product",\n                "labels": {\n                    "app": "appname"\n                }\n            },\n            "spec": {\n                "ports": [{\n                    "port": 8080,\n                    "targetPort": 8080,\n                    "protocol": "TCP",\n                    "name": "http"\n                }],\n                "selector": {\n                    "app": "appname"\n                }\n            }\n        }\n}', 'desc': '', 'parent': 'YAML'},
            {'key': 'APP_PERM_AUDITOR', 'value': '', 'extra': '{}',
                'desc': '应用权限申请指定审核人\n当前指定审核人会收到飞书通知，不发送通知到其余运维成员（仍有执行权限）'},
            {'key': 'JARFILE', 'value': 'jardependency/Jenkinsfile', 'extra': '{}',
                'desc': 'jar依赖包构建使用的Jenkinsfile，默认为jardependency/Jenkinsfile'},
            {'key': 'KUBERNETES_HEALTH', 'value': 'Kubernetes健康检测', 'extra': '[\n    {"name": "tcp",\n    "label": "TCP",\n    "items": [\n        {\n        "name": "readinessProbe",\n        "label": "就绪探针",\n        "enable": true,\n        "items": [\n            {"name": "initialDelaySeconds", "value": 10},\n            {"name": "periodSeconds", "value": 3},\n            {"name": "timeoutSeconds", "value": 5},\n            {"name": "tcpSocket__port", "value": "8080"}\n            ]\n        },\n        {\n        "name": "livenessProbe",\n        "label": "存活探针",\n        "enable": true,\n        "items": [\n            {"name": "initialDelaySeconds", "value": 600},\n            {"name": "periodSeconds", "value": 10},\n            {"name": "timeoutSeconds", "value": 20},\n            {"name": "tcpSocket__port", "value": "8080"}\n            ]\n        }\n    ]\n    },\n    {"name": "http",\n    "label": "HTTP",\n    "items": [\n        {\n            "name": "readinessProbe",\n            "label": "就绪探针",\n            "enable": true,\n            "items": [\n                {"name": "initialDelaySeconds", "value": 60},\n                {"name": "periodSeconds", "value": 3},\n                {"name": "timeoutSeconds", "value": 6},\n                {"name": "httpGet__path", "type": "input", "value": "/actuator/health"},\n                {"name": "httpGet__port", "value": "8070"}\n            ]\n        },\n        {\n            "name": "livenessProbe",\n            "label": "存活探针",\n            "enable": true,\n            "items": [\n                {"name": "initialDelaySeconds", "value": 600},\n                {"name": "periodSeconds", "value": 10},\n                {"name": "timeoutSeconds", "value": 20},\n                {"name": "httpGet__path", "type": "input", "value": "/actuator/health"},\n                {"name": "httpGet__port", "value": "8070"}\n            ]\n        }\n    ]\n    },\n        {"name": "command",\n    "label": "COMMAND",\n    "items": [\n        {\n            "name": "readinessProbe",\n            "label": "就绪探针",\n            "enable": true,\n            "items": [\n                {"name": "initialDelaySeconds", "value": 10},\n                {"name": "periodSeconds", "value": 3},\n                {"name": "timeoutSeconds", "value": 6},\n                {"name": "exec__command", "type": "input", "value": "echo HelloWorld."}\n            ]\n        },\n        {\n            "name": "livenessProbe",\n            "label": "存活探针",\n            "enable": true,\n            "items": [\n                {"name": "initialDelaySeconds", "value": 600},\n                {"name": "periodSeconds", "value": 10},\n                {"name": "timeoutSeconds", "value": 20},\n                {"name": "exec__command", "type": "input", "value": "echo HelloWorld."}\n            ]\n        }\n    ]\n    }\n]', 'desc': ''},
            {'key': 'PUBLISH_NOTIFY', 'value': 'CICD机器人',
                'extra': '{}', 'desc': '指定工单发版通知群'},
            {'key': 'publish_notify.devops', 'value': '平台自更新', 'extra': '{}',
                'desc': '指定产品工单发版通知群', 'parent': 'PUBLISH_NOTIFY'},
            {'key': 'publish_notify.charge', 'value': '充电桩构建发布',
                'extra': '{}', 'desc': '', 'parent': 'PUBLISH_NOTIFY'},
            {'key': 'DATABASE', 'value': '数据库', 'extra': '{}', 'desc': ''},
            {'key': 'database.db_type', 'value': '数据库类型',
                'extra': '[\n    {"name": "mysql", "label": "MySQL"},\n    {"name": "mssql", "label": "MsSQL"},\n    {"name": "redis", "label": "redis"}\n]', 'desc': '', 'parent': 'DATABASE'},
            {'key': 'CONTAINER', 'value': '多容器配置', 'extra': '{}', 'desc': '多容器配置'},
            {'key': 'container.log-agent', 'value': '日志agent',
                'extra': '{\r\n    "name": "log-agent",\r\n    "image": "docker.imaojia.com/basic/log-agent:v3",\r\n    "env": [{"name": "nacosAddr", "value": "nacos.hera-namespace.svc.cluster.local:80"}],\r\n    "volumeMounts": [{"mountPath": "/data/logs", "name": "logs"},{"mountPath": "/home/<USER>", "name": "log-agent"}],\r\n    "resources": {"limits": {"cpu": "50m", "memory": "0.5Gi"}}\r\n}', 'desc': '', 'parent': 'CONTAINER'},
            {'key': 'container.alpine', 'value': 'Alpine容器',
                'extra': '{\n    "name": "alpine",\n    "image": "docker.imaojia.com/basic/python:3.9.14-alpine3.16",\n    "env": [{"name": "ctype", "value": "devops"}],\n    "volumeMounts": [{"mountPath": "/data/logs", "name": "logs"}],\n    "resources": {"limits": {"cpu": "50m", "memory": "0.5Gi"}}\n}', 'desc': '', 'parent': 'CONTAINER'},
            {'key': 'JIRA', 'value': 'jira相关配置', 'extra': '{}', 'desc': ''},
            {'key': 'jira.include_status', 'value': '处理中,待开发,挂起中,重新打开', 'extra': '{}',
                'desc': '过滤jira状态，用于要查询的状态，多个状态用英文符号,分隔', 'parent': 'JIRA'},
            {'key': 'jira.exclude_status', 'value': '完成,已解决', 'extra': '{}',
                'desc': '过滤jira状态，用于要排除的状态，多个状态用英文逗号  , 分隔；如果不需要排除，则将参数值设为None', 'parent': 'JIRA'},
            {'key': 'jira.branch_regex', 'value': 'master,test', 'extra': '{}',
                'desc': '匹配分支；匹配到的保护分支合并时会自动关闭jira任务\n*表示匹配所有', 'parent': 'JIRA'},
            {'key': 'jira.complete_flag', 'value': '已完成,解决,已解决', 'extra': '{}',
                'desc': 'jira任务完成标识，多个用英文逗号 , 分隔', 'parent': 'JIRA'},
            {'key': 'jira.tester_field', 'value': 'customfield_10403',
                'extra': '{}', 'desc': 'jira任务测试人员字段', 'parent': 'JIRA'},
            {'key': 'SQL', 'value': 'SQL工单配置', 'extra': '{}', 'desc': 'SQL工单'},
            {'key': 'sql.rollback_template', 'value': '12',
                'extra': '{}', 'desc': 'SQL回滚工单ID', 'parent': 'SQL'},
            {'key': 'KUBERNETES_LIFTCYCLE', 'value': 'kubernetes应用生命周期配置', 'extra': '[\n    {"name": "command",\n    "label": "COMMAND",\n    "items": [\n        {\n            "name": "postStart",\n            "label": "postStart",\n            "enable": false,\n            "items": [\n                {"name": "exec__command", "type": "input", "value": "echo HelloWorld."}\n            ]\n        },\n        {\n            "name": "preStop",\n            "label": "preStop",\n            "enable": false,\n            "items": [\n                {"name": "exec__command", "type": "input", "value": "echo HelloWorld."}\n            ]\n        }\n    ]\n    },\n    {"name": "httpGet",\n    "label": "HTTPGET",\n    "items": [\n        {\n            "name": "postStart",\n            "label": "postStart",\n            "enable": false,\n            "items": [\n                {"name": "httpGet__path", "type": "input", "value": "/login"},\n                {"name": "httpGet__port", "value": 80},\n                {"name": "httpGet__host", "type": "input", "value": "localhost"},\n                {"name": "httpGet__scheme", "type": "input", "value": "HTTP"}\n            ]\n        },\n        {\n            "name": "preStop",\n            "label": "preStop",\n            "enable": false,\n            "items": [\n                {"name": "httpGet__path", "type": "input", "value": "/login"},\n                {"name": "httpGet__port", "value": 80},\n                {"name": "httpGet__host", "type": "input", "value": "localhost"},\n                {"name": "httpGet__scheme", "type": "input", "value": "HTTP"}\n            ]\n        }\n    ]\n    }\n]', 'desc': ''},
            {'key': 'GIT', 'value': 'Git相关操作配置', 'extra': '{}', 'desc': ''},
            {'key': 'git.envformr', 'value': '发版成功请求合并', 'extra': '{\n    "prod": "master",\n    "dev": "develop"\n}',
                'desc': '配置指定发版环境，在发布成功后创建合master的工单', 'parent': 'GIT'},
            {'key': 'git.mrexclude', 'value': 'devops.devops',
                'extra': '{}', 'desc': '发版成功后不创建合并的项目唯一ID', 'parent': 'GIT'},
            {'key': 'git.branch_env_map', 'value': '分支和环境映射', 'extra': '{\n    "develop": "dev", \n    "test": "test"\n}',
                'desc': '分支和环境映射，用于合并成功后自动进行CICD操作', 'parent': 'GIT'},
            {'key': 'ELASTICSEARCH', 'value': 'es相关配置', 'extra': '{}', 'desc': ''},
            {'key': 'elasticsearch.max_result_window', 'value': '1000000',
                'extra': '{}', 'desc': 'es最大查询数量', 'parent': 'ELASTICSEARCH'},
            {'key': 'elasticsearch.download_online_count', 'value': '1000',
                'extra': '{}', 'desc': '', 'parent': 'ELASTICSEARCH'},
            {'key': 'elasticsearch.longtext', 'value': 'message,logmsg',
                'extra': '{}', 'desc': 'es查询日志表格展示，长文本列宽自适应', 'parent': 'ELASTICSEARCH'},
            {'key': 'elasticsearch.logtimerange', 'value': '时间范围选择', 'extra': '[\n    { "index": "30-seconds", "item": "最近30秒" },\n    { "index": "1-minutes", "item": "最近1分钟" },\n    { "index": "10-minutes", "item": "最近10分钟" },\n    { "index": "30-minutes", "item": "最近30分钟" },\n    { "index": "1-hours", "item": "最近1小时" },\n    { "index": "6-hours", "item": "最近6小时" },\n    { "index": "12-hours", "item": "最近12小时" },\n    { "index": "24-hours", "item": "最近24小时" },\n    { "index": "7-days", "item": "最近7天" },\n    { "index": "1-months", "item": "最近1个月" } ,\n    { "index": "3-months", "item": "最近3个月" },\n    { "index": "6-months", "item": "最近6个月" }\n]', 'desc': '', 'parent': 'ELASTICSEARCH'}
        ]
        self.insert(self.model_datadict, 'DataDict', data, field='key')

    def handle(self, *args, **options):
        action = options['type']
        if action == 'rbac':
            self.init_rbac()
        elif action == 'datadict':
            self.init_datadict()
        elif action == 'system':
            self.init_system()
        elif action == 'all':
            self.init_system()
            self.init_rbac()
            self.init_datadict()
        else:
            raise CommandError('缺少参数，请查看帮助。')
